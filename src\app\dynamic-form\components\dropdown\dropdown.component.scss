/* Unified Dropdown Component Styles */

/* Base dropdown container */
.dropdown-container {
  position: relative;
  width: 100%;
}

/* Form Input Base Styles */
.form-input {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
  
  &:focus {
    outline: none;
    border-color: #9e9e9e;
    box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
  }
  
  &.invalid-input {
    border-color: #f44336;
    background-color: rgba(244, 67, 54, 0.05);
    
    &:focus {
      box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
    }
  }
  
  &::placeholder {
    color: #999;
  }
}

/* Disabled/Readonly Input Styles */
.form-input[disabled],
.form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
  pointer-events: none !important;
}

/* ID Dropdown Specific Styles */
.dropdown-type-id {
  .id-input-container {
    position: relative;
    display: flex;
    align-items: center;
    flex-grow: 1;
  }

  .id-input-container .form-input {
    flex-grow: 1;
    border-radius: 8px 0 0 8px;
    border-right: none;
  }

  .id-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 40px; /* Account for arrow button width */
    z-index: 1000;
    background-color: white;
    border: 1px solid #DBDBDB;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
  }

  .id-dropdown-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f1f3f4;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    color: #495057;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      color: #283A97;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .id-dropdown-empty,
  .id-dropdown-loading {
    padding: 12px 16px;
    color: #6c757d;
    font-style: italic;
    text-align: center;
  }

  /* ID Dropdown Scrollbar */
  .id-dropdown::-webkit-scrollbar {
    width: 6px;
  }

  .id-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .id-dropdown::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .id-dropdown::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

/* Type, Foreign Key, and Regular Dropdown Styles */
.dropdown-type-type,
.dropdown-type-foreignKey,
.dropdown-type-regular {
  .dropdown-input-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
  }

  .dropdown-input-container .form-input {
    flex-grow: 1;
    border-radius: 8px 0 0 8px;
    border-right: none;
    padding: 8px 12px;
  }

  .dropdown-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: white;
    border: 1px solid #DBDBDB;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
  }

  .dropdown-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f1f3f4;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    color: #495057;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      color: #283A97;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .dropdown-empty,
  .dropdown-loading {
    padding: 12px 16px;
    color: #6c757d;
    font-style: italic;
    text-align: center;
  }

  /* Dropdown List Scrollbar */
  .dropdown-list::-webkit-scrollbar {
    width: 6px;
  }

  .dropdown-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .dropdown-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .dropdown-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

/* Dropdown Arrow Button Styles */
.dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;

  &:hover {
    background-color: #e9ecef;
    color: #283A97;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
  }

  .mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    line-height: 1;
  }

  /* Disabled state */
  &:disabled {
    background-color: #f1f3f4 !important;
    color: #5f6368 !important;
    cursor: not-allowed !important;
    border-color: #dadce0 !important;
    opacity: 0.6 !important;

    &:hover {
      background-color: #f1f3f4 !important;
      color: #5f6368 !important;
      transform: none !important;
    }
  }
}

/* Loading state for dropdown lists */
.dropdown-loading,
.id-dropdown-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

/* Ensure dropdown containers work well in multi-field and grouped contexts */
.multi-input .dropdown-container,
.group-fields .dropdown-container {
  width: 100%;
  min-width: 200px;
}

/* Adjust dropdown positioning in grouped fields */
.grouped-field-section .dropdown-list,
.grouped-field-section .id-dropdown {
  z-index: 1001;
}

/* Ensure dropdowns don't overflow in small containers */
.dropdown-container {
  max-width: 100%;
  overflow: visible;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .dropdown-arrow-btn {
    width: 36px;
    height: 36px;

    .mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }

  .dropdown-item,
  .id-dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-empty,
  .dropdown-loading,
  .id-dropdown-empty,
  .id-dropdown-loading {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-type-id .id-dropdown {
    right: 36px;
  }

  .form-input {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 10px 12px;
  }
}

@media (max-width: 480px) {
  .dropdown-arrow-btn {
    width: 32px;
    height: 32px;

    .mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }

  .dropdown-item,
  .id-dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-empty,
  .dropdown-loading,
  .id-dropdown-empty,
  .id-dropdown-loading {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-type-id .id-dropdown {
    right: 32px;
  }

  .form-input {
    padding: 8px 10px;
    font-size: 14px;
  }
}
