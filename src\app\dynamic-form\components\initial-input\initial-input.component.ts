import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

// Import the unified dropdown component
import { DropdownComponent, DropdownConfig } from '../dropdown/dropdown.component';

@Component({
  selector: 'app-initial-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    DropdownComponent
  ],
  templateUrl: './initial-input.component.html',
  styleUrl: './initial-input.component.scss'
})
export class InitialInputComponent implements OnInit, OnDestroy {
  @Input() form!: FormGroup;
  @Input() tableName!: string;
  @Input() screenName!: string;
  @Input() showValidation: boolean = false;

  @Output() loadDataAndBuildForm = new EventEmitter<void>();
  @Output() viewData = new EventEmitter<void>();
  @Output() validationChange = new EventEmitter<boolean>();

  // Dropdown configuration for the unified dropdown component
  idDropdownConfig: DropdownConfig = {
    type: 'id',
    fieldName: 'ID',
    placeholder: 'Enter ID',
    showArrowButton: true,
    searchEnabled: true,
    debounceTime: 300
  };

  ngOnInit() {
    // Set up dropdown configuration with table/screen name
    this.idDropdownConfig = {
      ...this.idDropdownConfig,
      tableName: this.tableName,
      screenName: this.screenName
    };
  }

  ngOnDestroy() {
    // Cleanup is now handled by the unified dropdown component
  }

  // Event handlers for the unified dropdown component
  onIdOptionSelected(event: {option: any, fieldName: string}): void {
    // Reset validation state when a valid ID is selected
    this.showValidation = false;
    this.validationChange.emit(false);
  }

  onIdValidationChange(isValid: boolean): void {
    this.showValidation = !isValid;
    this.validationChange.emit(!isValid);
  }

  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  // Button click handlers

  onAddClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  onEditClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  onViewClick(): void {
    this.viewData.emit();
  }

  onMaintenanceClick(): void {
    // Placeholder for maintenance functionality
  }
}
