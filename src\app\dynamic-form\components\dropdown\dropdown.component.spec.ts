import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Form<PERSON><PERSON>er, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { DropdownComponent, DropdownConfig, DropdownOption } from './dropdown.component';
import { environment } from '../../../../environments/environment';

describe('DropdownComponent', () => {
  let component: DropdownComponent;
  let fixture: ComponentFixture<DropdownComponent>;
  let httpMock: HttpTestingController;
  let formBuilder: FormBuilder;
  let testForm: FormGroup;

  const mockOptions: DropdownOption[] = [
    { ROW_ID: 'option1', name: 'Option 1' },
    { ROW_ID: 'option2', name: 'Option 2' },
    { ROW_ID: 'option3', name: 'Option 3' }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        DropdownComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DropdownComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    formBuilder = TestBed.inject(FormBuilder);

    // Setup test form
    testForm = formBuilder.group({
      testField: ['']
    });

    // Setup basic component configuration
    component.form = testForm;
    component.config = {
      type: 'regular',
      fieldName: 'testField',
      placeholder: 'Test placeholder'
    };
    component.options = mockOptions;
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default configuration', () => {
      component.ngOnInit();
      
      expect(component.config.searchEnabled).toBe(true);
      expect(component.config.debounceTime).toBe(300);
      expect(component.config.showArrowButton).toBe(true);
    });

    it('should initialize filtered options from input options', () => {
      component.ngOnInit();
      
      expect(component.filteredOptions).toEqual(mockOptions);
    });

    it('should disable form control when isViewMode is true', () => {
      component.isViewMode = true;
      component.ngOnInit();
      
      const formControl = testForm.get('testField');
      expect(formControl?.disabled).toBe(true);
    });
  });

  describe('ID Dropdown Type', () => {
    beforeEach(() => {
      component.config = {
        type: 'id',
        fieldName: 'testField',
        tableName: 'testTable'
      };
    });

    it('should render ID dropdown template', () => {
      fixture.detectChanges();
      
      const idContainer = fixture.debugElement.query(By.css('.id-input-container'));
      expect(idContainer).toBeTruthy();
    });

    it('should emit validation change on input for ID dropdown', () => {
      spyOn(component.validationChange, 'emit');
      
      const event = new Event('input');
      Object.defineProperty(event, 'target', {
        value: { value: 'test' }
      });
      
      component.onInputChange(event);
      
      expect(component.validationChange.emit).toHaveBeenCalledWith(false);
    });

    it('should load ID options from API', () => {
      component.loadAllOptions();
      
      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testTable`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ _select: ["ROW_ID"] });
      
      req.flush(mockOptions);
      
      expect(component.filteredOptions).toEqual(mockOptions);
      expect(component.showDropdown).toBe(true);
    });
  });

  describe('Type Dropdown', () => {
    beforeEach(() => {
      component.config = {
        type: 'type',
        fieldName: 'testField'
      };
    });

    it('should render type dropdown template', () => {
      fixture.detectChanges();
      
      const dropdownContainer = fixture.debugElement.query(By.css('.dropdown-input-container'));
      expect(dropdownContainer).toBeTruthy();
    });

    it('should load type options from fieldType API', () => {
      component.loadAllOptions();
      
      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`);
      expect(req.request.method).toBe('POST');
      
      req.flush(mockOptions);
      
      expect(component.filteredOptions).toEqual(mockOptions);
    });

    it('should filter type options on search', () => {
      component.searchOptions('option1');
      
      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`);
      req.flush(mockOptions);
      
      expect(component.filteredOptions).toEqual([mockOptions[0]]);
    });
  });

  describe('Foreign Key Dropdown', () => {
    beforeEach(() => {
      component.config = {
        type: 'foreignKey',
        fieldName: 'testField'
      };
    });

    it('should load foreign key options from formDefinition API', () => {
      component.loadAllOptions();
      
      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=formDefinition`);
      expect(req.request.method).toBe('POST');
      
      req.flush(mockOptions);
      
      expect(component.filteredOptions).toEqual(mockOptions);
    });
  });

  describe('Regular Dropdown', () => {
    beforeEach(() => {
      component.config = {
        type: 'regular',
        fieldName: 'testField'
      };
      component.fields = [
        { fieldName: 'testField', foreginKey: 'customQueryBuilder' }
      ];
    });

    it('should load regular options from custom foreign key API', () => {
      component.loadAllOptions();
      
      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=customQueryBuilder`);
      expect(req.request.method).toBe('POST');
      
      req.flush(mockOptions);
      
      expect(component.filteredOptions).toEqual(mockOptions);
    });
  });

  describe('User Interactions', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should show dropdown on input focus', () => {
      component.onInputFocus();
      
      expect(component.showDropdown).toBe(true);
    });

    it('should hide dropdown on input blur', (done) => {
      component.showDropdown = true;
      component.onInputBlur();
      
      setTimeout(() => {
        expect(component.showDropdown).toBe(false);
        done();
      }, 250);
    });

    it('should toggle dropdown visibility', () => {
      expect(component.showDropdown).toBe(false);
      
      component.toggleDropdown();
      expect(component.showDropdown).toBe(true);
      
      component.toggleDropdown();
      expect(component.showDropdown).toBe(false);
    });

    it('should select option and emit event', () => {
      spyOn(component.optionSelected, 'emit');
      const testOption = mockOptions[0];
      
      component.selectOption(testOption);
      
      expect(component.optionSelected.emit).toHaveBeenCalledWith({
        option: testOption,
        fieldName: 'testField'
      });
      expect(component.showDropdown).toBe(false);
    });

    it('should prevent interactions when disabled', () => {
      component.config.disabled = true;
      
      component.onInputChange(new Event('input'));
      component.onInputFocus();
      component.toggleDropdown();
      
      expect(component.showDropdown).toBe(false);
    });

    it('should prevent interactions when in view mode', () => {
      component.isViewMode = true;
      
      component.onInputChange(new Event('input'));
      component.onInputFocus();
      component.toggleDropdown();
      
      expect(component.showDropdown).toBe(false);
    });
  });

  describe('Search Functionality', () => {
    it('should debounce search input', (done) => {
      spyOn(component, 'searchOptions');
      
      const event = new Event('input');
      Object.defineProperty(event, 'target', {
        value: { value: 'test' }
      });
      
      component.onInputChange(event);
      component.onInputChange(event);
      component.onInputChange(event);
      
      setTimeout(() => {
        expect(component.searchOptions).toHaveBeenCalledTimes(1);
        expect(component.searchOptions).toHaveBeenCalledWith('test');
        done();
      }, 350);
    });

    it('should emit search performed event', () => {
      spyOn(component.searchPerformed, 'emit');
      
      component.searchOptions('test');
      
      expect(component.searchPerformed.emit).toHaveBeenCalledWith({
        searchTerm: 'test',
        fieldName: 'testField'
      });
    });
  });

  describe('Helper Methods', () => {
    it('should extract original field name correctly', () => {
      expect(component['extractOriginalFieldName']('field_nested_1_2')).toBe('field');
      expect(component['extractOriginalFieldName']('field_group_1')).toBe('field');
      expect(component['extractOriginalFieldName']('field_1')).toBe('field');
      expect(component['extractOriginalFieldName']('simpleField')).toBe('simpleField');
    });

    it('should extract query builder ID correctly', () => {
      component.config.tableName = 'table1,table2';
      expect(component['extractQueryBuilderId']()).toBe('table1');
      
      component.config.tableName = 'singleTable';
      expect(component['extractQueryBuilderId']()).toBe('singleTable');
    });

    it('should get option display text correctly', () => {
      const option = { ROW_ID: 'id1', name: 'Test Name' };
      expect(component['getOptionDisplayText'](option)).toBe('id1');
      
      const optionWithoutName = { ROW_ID: 'id2' };
      expect(component['getOptionDisplayText'](optionWithoutName)).toBe('id2');
    });

    it('should track options by ROW_ID', () => {
      const option = { ROW_ID: 'test-id' };
      expect(component.trackByOptionId(0, option)).toBe('test-id');
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', () => {
      component.loadAllOptions();
      
      const req = httpMock.expectOne(req => req.url.includes('/api/query-builder/search'));
      req.error(new ErrorEvent('Network error'));
      
      expect(component.filteredOptions).toEqual([]);
      expect(component.showDropdown).toBe(true);
      expect(component.isLoading).toBe(false);
    });

    it('should handle non-array API responses', () => {
      component.loadAllOptions();
      
      const req = httpMock.expectOne(req => req.url.includes('/api/query-builder/search'));
      req.flush({ error: 'Invalid response' });
      
      expect(component.filteredOptions).toEqual([]);
      expect(component.showDropdown).toBe(true);
    });
  });
});
