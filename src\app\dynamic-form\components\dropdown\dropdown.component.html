<!-- Unified Dropdown Component Template -->
<div class="dropdown-container" [ngClass]="'dropdown-type-' + config.type">
  
  <!-- ID Dropdown (Special styling for initial input) -->
  @if (config.type === 'id') {
    <div class="id-input-container">
      <input 
        [formControlName]="config.fieldName" 
        [id]="config.fieldName" 
        type="text" 
        class="form-input" 
        [class]="getInputClass()" 
        [placeholder]="config.placeholder || 'Enter ID'" 
        [disabled]="isViewMode || config.disabled || config.readonly"
        (input)="onInputChange($event)" 
        (focus)="onInputFocus()" 
        (blur)="onInputBlur()" 
        required />
      
      <!-- Arrow button to toggle dropdown -->
      @if (config.showArrowButton !== false) {
        <button 
          type="button" 
          class="dropdown-arrow-btn" 
          (click)="toggleDropdown()" 
          [disabled]="isViewMode || config.disabled || config.readonly"
          [matTooltip]="'Show ' + (config.type === 'id' ? 'ID' : config.type) + ' suggestions'">
          <mat-icon>{{ showDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
        </button>
      }
      
      <!-- ID Dropdown List -->
      @if (showDropdown) {
        <div class="id-dropdown">
          @if (isLoading) {
            <div class="id-dropdown-loading">
              Loading...
            </div>
          } @else if (filteredOptions && filteredOptions.length > 0) {
            @for (option of filteredOptions; track trackByOptionId($index, option)) {
              <div class="id-dropdown-item" (click)="selectOption(option)">
                {{ option.ID || option.ROW_ID }}
              </div>
            }
          } @else {
            <div class="id-dropdown-empty">
              No IDs found
            </div>
          }
        </div>
      }
    </div>
  }
  
  <!-- Type, Foreign Key, and Regular Dropdowns -->
  @else {
    <div class="dropdown-input-container">
      <input 
        [formControlName]="config.fieldName" 
        [id]="config.fieldName" 
        type="text" 
        class="form-input dropdown-input" 
        [placeholder]="config.placeholder || ('Search ' + config.type)"
        [disabled]="isViewMode || config.disabled || config.readonly"
        (input)="onInputChange($event)" 
        (focus)="onInputFocus()" 
        (blur)="onInputBlur()" />
      
      <!-- Arrow button to toggle dropdown -->
      @if (config.showArrowButton !== false) {
        <button 
          type="button" 
          class="dropdown-arrow-btn" 
          (click)="toggleDropdown()" 
          [disabled]="isViewMode || config.disabled || config.readonly"
          [matTooltip]="'Show ' + config.type + ' options'">
          <mat-icon>{{ showDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
        </button>
      }
      
      <!-- Dropdown List -->
      @if (showDropdown) {
        <div class="dropdown-list">
          @if (isLoading) {
            <div class="dropdown-loading">
              Loading...
            </div>
          } @else if (filteredOptions && filteredOptions.length > 0) {
            @for (option of filteredOptions; track trackByOptionId($index, option)) {
              <div class="dropdown-item" (click)="selectOption(option)">
                <!-- Type dropdown: show ROW_ID only -->
                @if (config.type === 'type') {
                  {{ option.ROW_ID }}
                }
                <!-- Foreign Key dropdown: show ROW_ID only -->
                @else if (config.type === 'foreignKey') {
                  {{ option.ROW_ID }}
                }
                <!-- Regular dropdown: show all keys -->
                @else {
                  @for (key of getKeys(option); track trackByKey($index, key)) {
                    {{ option[key] }}&nbsp;
                  }
                }
              </div>
            }
          } @else {
            <div class="dropdown-empty">
              @if (config.type === 'type') {
                No types found
              } @else if (config.type === 'foreignKey') {
                No foreign keys found
              } @else {
                No options found
              }
            </div>
          }
        </div>
      }
    </div>
  }
</div>
