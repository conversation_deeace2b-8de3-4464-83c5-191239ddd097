import { Component, Input, Output, EventEmitter, OnD<PERSON>roy, OnInit, OnChanges, inject, ChangeDetectorRef, SimpleChanges } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../environments/environment';

export interface DropdownOption {
  ROW_ID: string;
  [key: string]: any;
}

export interface DropdownConfig {
  type: 'id' | 'type' | 'foreignKey' | 'regular';
  fieldName: string;
  placeholder?: string;
  queryBuilderId?: string;
  foreignKey?: string;
  tableName?: string;
  screenName?: string;
  disabled?: boolean;
  readonly?: boolean;
  showArrowButton?: boolean;
  searchEnabled?: boolean;
  debounceTime?: number;
}

@Component({
  selector: 'app-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './dropdown.component.html',
  styleUrl: './dropdown.component.scss'
})
export class DropdownComponent implements OnInit, OnDestroy, OnChanges {
  @Input() config!: DropdownConfig;
  @Input() form!: FormGroup;
  @Input() options: DropdownOption[] = [];
  @Input() isViewMode: boolean = false;
  @Input() fields: any[] = []; // For extracting original field names in complex scenarios

  @Output() optionSelected = new EventEmitter<{option: DropdownOption, fieldName: string}>();
  @Output() searchPerformed = new EventEmitter<{searchTerm: string, fieldName: string}>();
  @Output() validationChange = new EventEmitter<boolean>();

  // Dropdown state
  showDropdown = false;
  filteredOptions: DropdownOption[] = [];
  searchTimeout: any;
  isLoading = false;

  // Performance optimization: API response cache
  private apiCache: { [key: string]: DropdownOption[] } = {};

  // Track when we're setting dropdown values to prevent input conflicts
  private settingDropdownValue = false;

  private http = inject(HttpClient);
  private cdr = inject(ChangeDetectorRef);

  ngOnInit() {
    // Initialize dropdown based on configuration
    this.initializeDropdown();
    
    // Preload dropdown data for performance
    this.preloadDropdownData();

    // Ensure form control is properly disabled when it should be
    this.updateFormControlDisabledState();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update form control disabled state when inputs change
    if (changes['isViewMode'] || changes['config']) {
      this.updateFormControlDisabledState();
    }

    // Update filtered options when options input changes
    if (changes['options'] && this.options) {
      this.filteredOptions = [...this.options];
    }
  }

  ngOnDestroy() {
    // Clear search timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  private initializeDropdown(): void {
    // Set default configuration values
    this.config = {
      searchEnabled: true,
      debounceTime: 300,
      showArrowButton: true,
      ...this.config
    };

    // Initialize filtered options
    if (this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
    }
  }

  private updateFormControlDisabledState(): void {
    const formControl = this.form.get(this.config.fieldName);
    if (formControl) {
      const shouldDisable = this.isViewMode || this.config.disabled || this.config.readonly;
      if (shouldDisable && formControl.enabled) {
        formControl.disable();
      } else if (!shouldDisable && formControl.disabled) {
        formControl.enable();
      }
    }
  }

  // Input event handlers
  onInputChange(event: Event): void {
    // Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.config.disabled || this.config.readonly) {
      return;
    }

    // Skip if we're currently setting a dropdown value
    if (this.settingDropdownValue) {
      return;
    }

    const input = event.target as HTMLInputElement;
    const value = input.value;

    // Emit validation change for ID dropdowns
    if (this.config.type === 'id' && value && value.trim() !== '') {
      this.validationChange.emit(false);
    }

    // Clear previous timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Set a new timeout to avoid too many API calls
    this.searchTimeout = setTimeout(() => {
      this.searchOptions(value);
    }, this.config.debounceTime || 300);
  }

  onInputFocus(): void {
    // Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.config.disabled || this.config.readonly) {
      return;
    }

    const currentValue = this.form.get(this.config.fieldName)?.value || '';
    if (currentValue.trim() === '') {
      this.loadAllOptions();
    } else {
      this.searchOptions(currentValue);
    }
  }

  onInputBlur(): void {
    // Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.config.disabled || this.config.readonly) {
      return;
    }

    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showDropdown = false;
    }, 200);
  }

  toggleDropdown(): void {
    // Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.config.disabled || this.config.readonly) {
      return;
    }

    if (!this.showDropdown) {
      const currentValue = this.form.get(this.config.fieldName)?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllOptions();
      } else {
        this.searchOptions(currentValue);
      }
    } else {
      this.showDropdown = false;
    }
  }

  // Option selection
  selectOption(option: DropdownOption): void {
    this.setDropdownValue(option);
    this.optionSelected.emit({ option, fieldName: this.config.fieldName });
  }

  // Search and data loading methods
  searchOptions(searchTerm: string): void {
    this.searchPerformed.emit({ searchTerm, fieldName: this.config.fieldName });

    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    // Use different search strategies based on dropdown type
    switch (this.config.type) {
      case 'id':
        this.searchIdOptions(searchTerm);
        break;
      case 'type':
        this.searchTypeOptions(searchTerm);
        break;
      case 'foreignKey':
        this.searchForeignKeyOptions(searchTerm);
        break;
      case 'regular':
        this.searchRegularOptions(searchTerm);
        break;
    }
  }

  loadAllOptions(): void {
    // Use different loading strategies based on dropdown type
    switch (this.config.type) {
      case 'id':
        this.loadAllIds();
        break;
      case 'type':
        this.loadAllTypes();
        break;
      case 'foreignKey':
        this.loadAllForeignKeys();
        break;
      case 'regular':
        this.loadAllRegularOptions();
        break;
    }
  }

  // Type-specific search methods
  private searchIdOptions(searchTerm: string): void {
    const queryBuilderId = this.extractQueryBuilderId();
    this.loadAllAndFilter(queryBuilderId, searchTerm);
  }

  private searchTypeOptions(searchTerm: string): void {
    // fieldType API doesn't support server-side filtering, use client-side filtering
    this.loadAllAndFilter('fieldType', searchTerm);
  }

  private searchForeignKeyOptions(searchTerm: string): void {
    // formDefinition API doesn't support server-side filtering, use client-side filtering
    this.loadAllAndFilter('formDefinition', searchTerm);
  }

  private searchRegularOptions(searchTerm: string): void {
    const originalFieldName = this.extractOriginalFieldName(this.config.fieldName);
    const field = this.fields.find(f => f.fieldName === originalFieldName);
    if (!field || !field.foreginKey) return;

    this.loadAllAndFilter(field.foreginKey, searchTerm);
  }

  // Type-specific load all methods
  private loadAllIds(): void {
    const queryBuilderId = this.extractQueryBuilderId();
    this.loadFromApi(queryBuilderId);
  }

  private loadAllTypes(): void {
    this.loadFromApi('fieldType');
  }

  private loadAllForeignKeys(): void {
    this.loadFromApi('formDefinition');
  }

  private loadAllRegularOptions(): void {
    const originalFieldName = this.extractOriginalFieldName(this.config.fieldName);
    const field = this.fields.find(f => f.fieldName === originalFieldName);
    if (!field || !field.foreginKey) return;

    this.loadFromApi(field.foreginKey);
  }

  // Core API methods
  private loadFromApi(queryBuilderId: string): void {
    const cacheKey = queryBuilderId;

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredOptions = this.apiCache[cacheKey];
      this.showDropdown = true;
      return;
    }

    // Fallback: Load if not preloaded
    this.isLoading = true;
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = { _select: ["ROW_ID"] };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          this.filteredOptions = response;
          this.showDropdown = true;
        } else {
          this.filteredOptions = [];
          this.showDropdown = true;
        }
      },
      error: () => {
        this.isLoading = false;
        this.filteredOptions = [];
        this.showDropdown = true;
      }
    });
  }

  private loadAllAndFilter(queryBuilderId: string, searchTerm: string): void {
    const cacheKey = queryBuilderId;

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option =>
        option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
      );
      this.filteredOptions = filtered;
      this.showDropdown = true;
      return;
    }

    // Fallback: Load if not preloaded
    this.isLoading = true;
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = { _select: ["ROW_ID"] };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          const filtered = response.filter(option =>
            option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
          );
          this.filteredOptions = filtered;
          this.showDropdown = true;
        } else {
          this.filteredOptions = [];
          this.showDropdown = true;
        }
      },
      error: () => {
        this.isLoading = false;
        this.filteredOptions = [];
        this.showDropdown = true;
      }
    });
  }

  // Value setting and form control management
  private setDropdownValue(option: DropdownOption): void {
    // Mark that we're setting a dropdown value to prevent input conflicts
    this.settingDropdownValue = true;

    const formControl = this.form.get(this.config.fieldName);
    if (formControl) {
      // Get the display text for the input field
      const displayText = this.getOptionDisplayText(option);

      // Set the form control value to ROW_ID (for storage)
      formControl.setValue(option.ROW_ID);

      // Set the input element's display value to the human-readable text
      setTimeout(() => {
        const inputElement = document.getElementById(this.config.fieldName) as HTMLInputElement;
        if (inputElement) {
          inputElement.value = displayText;
        }
      }, 0);

      // Force change detection and validation
      formControl.markAsDirty();
      formControl.markAsTouched();
      formControl.updateValueAndValidity();

      // Force Angular change detection
      this.cdr.detectChanges();

      // Reset validation state for ID dropdowns
      if (this.config.type === 'id') {
        this.validationChange.emit(false);
      }
    }

    // Close the dropdown
    this.showDropdown = false;

    // Clear the dropdown value setting flag after a short delay
    setTimeout(() => {
      this.settingDropdownValue = false;
    }, 100);
  }

  // Utility methods
  getInputClass(): string {
    // For ID dropdowns, return validation class if needed
    if (this.config.type === 'id') {
      // This would need to be passed in or determined based on validation state
      return '';
    }
    return '';
  }

  getKeys(option: DropdownOption): string[] {
    return Object.keys(option);
  }

  trackByOptionId(_index: number, option: DropdownOption): string {
    return option.ROW_ID;
  }

  trackByKey(_index: number, key: string): string {
    return key;
  }

  // Helper methods
  private getOptionDisplayText(option: DropdownOption): string {
    if (!option) return '';

    // For type fields, just return the ROW_ID
    if (typeof option.ROW_ID === 'string') {
      return option.ROW_ID;
    }

    // For other foreign key fields, get the first non-ROW_ID property
    const keys = Object.keys(option).filter(key => key !== 'ROW_ID');
    if (keys.length > 0) {
      return option[keys[0]] || option.ROW_ID || '';
    }

    return option.ROW_ID || '';
  }

  private extractOriginalFieldName(fieldName: string): string {
    if (fieldName.includes('_nested_')) {
      return fieldName.split('_nested_')[0];
    } else if (fieldName.includes('_group_')) {
      return fieldName.split('_group_')[0];
    } else if (fieldName.includes('_')) {
      // Handle simple multi-field pattern like fieldName_j
      const parts = fieldName.split('_');
      if (parts.length === 2 && !isNaN(parseInt(parts[1]))) {
        return parts[0];
      }
      return fieldName;
    }
    return fieldName;
  }

  private extractQueryBuilderId(): string {
    const nameToUse = this.config.tableName || this.config.screenName || this.config.queryBuilderId;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse || '';
  }

  // Performance optimization: preload dropdown data
  private preloadDropdownData(): void {
    let queryBuilderId = '';

    switch (this.config.type) {
      case 'id':
        queryBuilderId = this.extractQueryBuilderId();
        break;
      case 'type':
        queryBuilderId = 'fieldType';
        break;
      case 'foreignKey':
        queryBuilderId = 'formDefinition';
        break;
      case 'regular':
        const originalFieldName = this.extractOriginalFieldName(this.config.fieldName);
        const field = this.fields.find(f => f.fieldName === originalFieldName);
        if (field && field.foreginKey) {
          queryBuilderId = field.foreginKey;
        }
        break;
    }

    if (queryBuilderId) {
      this.preloadApiData(queryBuilderId);
    }
  }

  private preloadApiData(queryBuilderId: string): void {
    const cacheKey = queryBuilderId;

    // Skip if already cached
    if (this.apiCache[cacheKey]) {
      return;
    }

    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = { _select: ["ROW_ID"] };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
        }
      },
      error: () => {
        // Handle preload error silently
      }
    });
  }
}
